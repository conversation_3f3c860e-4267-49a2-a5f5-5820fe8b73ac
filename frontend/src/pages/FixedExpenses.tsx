import { CloseOutlined, DeleteOutlined, EditOutlined, SaveOutlined } from '@ant-design/icons';
import {
  Tag as AntdTag,
  Button,
  DatePicker,
  Form,
  FormInstance,
  Input,
  InputNumber,
  message,
  Popconfirm,
  Select,
  Space,
  Table,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { DATE_FORMAT } from '../constants/dateFormats';
import ClearFiltersButton from '../components/common/clearFiltersButton';
import { CurrencyInput } from '../components/common/CurrencyInput';
import { FormCard } from '../components/common/FormCard';
import { LoadingTable } from '../components/common/LoadingTable';
import { useOrganization } from '../contexts/OrganizationContext';
import { useTableControls } from '../hooks/useTableControls';
import {
  addFixedExpense,
  createTag,
  deleteFixedExpense,
  getFixedExpenses,
  getTags,
  updateFixedExpense,
} from '../services/api';
import { FixedExpense, Tag, UpsertFixedExpense } from '../types';
import { TableColumnType } from '../types/table';
import { formatCurrency, formatRecurrence, parseCurrency } from '../utils/helpers';
import {
  getColumnSearchProps,
  getDateRangeSearchProps,
  getNumericColumnSearchProps,
  getTagColumnSearchProps,
} from '../utils/tableFilters';

const { Title } = Typography;
const { Option } = Select;

const FixedExpenses: React.FC = () => {
  const [form] = Form.useForm<UpsertFixedExpense>();
  const [expenses, setExpenses] = useState<FixedExpense[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [editingKey, setEditingKey] = useState<string | number>('');
  const [editForm] = Form.useForm();
  const [total, setTotal] = useState<number>(0);
  const [allTags, setAllTags] = useState<Tag[]>([]);
  const [tagOptions, setTagOptions] = useState<{ label: string; value: string }[]>([]);
  const { filteredInfo, sortedInfo, handleTableChange, clearFiltersAndSorting } = useTableControls({
    initialSort: { columnKey: 'date', order: 'descend' },
  });
  const { organization } = useOrganization();

  useEffect(() => {
    fetchExpenses();
    fetchAllTags();
  }, []);

  useEffect(() => {
    const sum = expenses.reduce((acc, expense) => acc + expense.amount, 0);
    setTotal(sum);
  }, [expenses]);

  // Add CSS for table cell alignment
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .fixed-expenses-table .ant-table-tbody > tr > td {
        vertical-align: top !important;
        padding-top: 8px !important;
      }
      .fixed-expenses-table .ant-form-item {
        margin-bottom: 0 !important;
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const fetchAllTags = async () => {
    try {
      const response = await getTags();
      const sortedTags = response.data.sort((a: Tag, b: Tag) => a.name.localeCompare(b.name));
      setAllTags(sortedTags);
      setTagOptions(
        sortedTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );
    } catch (error: any) {
      console.error('Error fetching tags:', error);
      message.error('Failed to load tags');
    }
  };

  const fetchExpenses = async (): Promise<void> => {
    try {
      setLoading(true);
      const response = await getFixedExpenses();
      const expensesData = Array.isArray(response.data) ? response.data : [];
      setExpenses(expensesData);
    } catch (error: any) {
      console.error('Error fetching fixed expenses:', error);
      const errorDetail = error.response?.data?.detail;
      const errorMessage =
        typeof errorDetail === 'object'
          ? errorDetail.msg || 'Failed to load fixed expenses'
          : errorDetail || 'Failed to load fixed expenses';
      message.error(errorMessage);
      setExpenses([]);
    } finally {
      setLoading(false);
    }
  };

  const processTags = async (tags: string[] | undefined): Promise<number[]> => {
    if (!organization) {
      message.error('Organization not found');
      return [];
    }
    try {
      if (!tags) {
        return [];
      }
      const tagIds: number[] = [];
      const newTags: Tag[] = [];
      for (const tagName of tags) {
        const existingTag = allTags.find(tag => tag.name === tagName);
        if (existingTag) {
          tagIds.push(existingTag.id);
        } else {
          const trimmedTagName = tagName.trim();
          const response = await createTag({
            id: 0,
            name: trimmedTagName,
            organization_id: organization.id,
          });
          const tagObject = response.data;
          if (tagObject) {
            tagIds.push(tagObject.id);
            newTags.push(tagObject);
          }
        }
      }

      const updatedTags = [...allTags, ...newTags].sort((a, b) => a.name.localeCompare(b.name));
      setAllTags(updatedTags);
      setTagOptions(
        updatedTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );

      return tagIds;
    } catch (error) {
      console.error('Error processing tags:', error);
      message.error('Failed to process tags');
      return [];
    }
  };

  const handleSubmit = async (values: UpsertFixedExpense): Promise<void> => {
    try {
      setSubmitting(true);
      const tagIds = await processTags(values.tag_names);
      const { tags, tag_names, ...rest } = values;
      const formattedValues = {
        ...rest,
        amount: parseCurrency(values.amount),
        tag_ids: tagIds,
      };
      await addFixedExpense(formattedValues as UpsertFixedExpense);
      message.success('Fixed expense added successfully');
      form.resetFields();
      await fetchExpenses();
    } catch (error: any) {
      console.error('Error adding fixed expense:', error);
      const errorMessage = error.response?.data?.detail || 'Failed to add fixed expense';
      message.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (id: string | number): Promise<void> => {
    try {
      await deleteFixedExpense(id);
      message.success('Fixed expense deleted successfully');
      await fetchExpenses();
    } catch (error: any) {
      console.error('Error deleting fixed expense:', error);
      const errorMessage = error.response?.data?.detail || 'Failed to delete fixed expense';
      message.error(errorMessage);
    }
  };

  const isEditing = (record: FixedExpense): boolean => record.id === editingKey;

  const edit = (record: FixedExpense): void => {
    editForm.setFieldsValue({
      ...record,
      amount: record.amount,
      recurrence: record.recurrence ?? 'None',
      interval: record.interval,
      tag_names: record.tags ? record.tags.map(tag => tag.name) : [],
    });
    setEditingKey(record.id);
  };

  const cancel = (): void => {
    setEditingKey('');
  };

  const save = async (id: string | number): Promise<void> => {
    try {
      const row = (await editForm.validateFields()) as UpsertFixedExpense;
      const tagIds = await processTags(row.tag_names);
      const { tags, tag_names, id: rowId, ...rest } = row;
      const updatedExpense = {
        id: id as number,
        ...rest,
        tag_ids: tagIds,
      };
      await updateFixedExpense(updatedExpense as UpsertFixedExpense);
      message.success('Fixed expense updated successfully');
      setEditingKey('');
      await fetchExpenses();
    } catch (errInfo) {
      console.log('Validate Failed:', errInfo);
      message.error('Failed to update expense. Please check the form fields.');
    }
  };

  const handleRecurrenceChange = (value: string, formInstance: FormInstance): void => {
    if (value !== 'Custom') {
      formInstance.setFieldsValue({ interval: undefined });
    }
    // Clear week/day fields if not monthly or biweekly
    if (value !== 'Monthly' && value !== 'Bi-weekly') {
      formInstance.setFieldsValue({
        week_of_month: undefined,
        day_of_week: undefined,
      });
    }
  };

  const columns: TableColumnType<FixedExpense>[] = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: '12%',
      sorter: (a, b) => a.date.unix() - b.date.unix(),
      sortOrder: sortedInfo.columnKey === 'date' && sortedInfo.order,
      filteredValue: filteredInfo.date || null,
      ...getDateRangeSearchProps('date'),
      defaultSortOrder: 'descend' as const,
      render: (date: dayjs.Dayjs, record: FixedExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='date' rules={[{ required: true, message: 'Please select a date' }]}>
            <DatePicker format={DATE_FORMAT} style={{ width: '100%' }} />
          </Form.Item>
        ) : (
          date.format(DATE_FORMAT)
        );
      },
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: '25%',
      sorter: (a, b) => a.description.localeCompare(b.description),
      sortOrder: sortedInfo.columnKey === 'description' && sortedInfo.order,
      filteredValue: filteredInfo.description || null,
      ...getColumnSearchProps('description', 'Description'),
      render: (description: string, record: FixedExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='description' rules={[{ required: true }]}>
            <Input.TextArea rows={1} placeholder='Enter description' />
          </Form.Item>
        ) : (
          description
        );
      },
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      width: '12%',
      sorter: (a, b) => a.amount - b.amount,
      sortOrder: sortedInfo.columnKey === 'amount' && sortedInfo.order,
      filteredValue: filteredInfo.amount || null,
      ...getNumericColumnSearchProps('amount'),
      render: (amount: number, record: FixedExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='amount' rules={[{ required: true }]}>
            <CurrencyInput />
          </Form.Item>
        ) : (
          formatCurrency(amount)
        );
      },
    },
    {
      title: 'Recurrence',
      dataIndex: 'recurrence',
      key: 'recurrence',
      width: '15%',
      sorter: (a, b) => (a.recurrence || '').localeCompare(b.recurrence || ''),
      sortOrder: sortedInfo.columnKey === 'recurrence' && sortedInfo.order,
      filteredValue: filteredInfo.recurrence || null,
      filters: [
        { text: 'One-time', value: 'None' },
        { text: 'Weekly', value: 'Weekly' },
        { text: 'Bi-weekly', value: 'Bi-weekly' },
        { text: 'Monthly', value: 'Monthly' },
        { text: 'Quarterly', value: 'Quarterly' },
        { text: 'Custom', value: 'Custom' },
      ],
      onFilter: (value: any, record: FixedExpense) => (record.recurrence || 'None') === value,
      render: (recurrence: string, record: FixedExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='recurrence'>
            <Select onChange={value => handleRecurrenceChange(value, editForm)} allowClear>
              <Option value='None'>None</Option>
              <Option value='Weekly'>Weekly</Option>
              <Option value='Bi-weekly'>Bi-weekly</Option>
              <Option value='Monthly'>Monthly</Option>
              <Option value='Quarterly'>Quarterly</Option>
              <Option value='Custom'>Custom</Option>
            </Select>
          </Form.Item>
        ) : (
          formatRecurrence(recurrence, record.interval)
        );
      },
    },
    {
      title: 'Interval (days)',
      dataIndex: 'interval',
      key: 'interval',
      width: '10%',
      sorter: (a, b) => (a.interval || 0) - (b.interval || 0),
      sortOrder: sortedInfo.columnKey === 'interval' && sortedInfo.order,
      filteredValue: filteredInfo.interval || null,
      ...getNumericColumnSearchProps('interval', false),
      render: (interval: number, record: FixedExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.recurrence !== currentValues.recurrence
            }
          >
            {() => (
              <Form.Item name='interval'>
                <InputNumber
                  disabled={editForm.getFieldValue('recurrence') !== 'Custom'}
                  min={1}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            )}
          </Form.Item>
        ) : (
          interval
        );
      },
    },

    {
      title: 'Tags',
      dataIndex: 'tags',
      key: 'tags',
      width: '18%',
      ...getTagColumnSearchProps(allTags),
      filteredValue: filteredInfo.tags || null,
      render: (tags: Tag[], record: FixedExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='tag_names'>
            <Select
              mode='tags'
              style={{ width: '100%' }}
              placeholder='Select or create tags'
              tokenSeparators={[',']}
              options={tagOptions}
            />
          </Form.Item>
        ) : (
          <>
            {(tags || []).map(tag => (
              <AntdTag color='blue' key={tag.id}>
                {tag.name}
              </AntdTag>
            ))}
          </>
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '8%',
      render: (_: any, record: FixedExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button
              icon={<SaveOutlined />}
              type='primary'
              onClick={() => save(record.id)}
              size='small'
            >
              Save
            </Button>
            <Button icon={<CloseOutlined />} onClick={cancel} size='small'>
              Cancel
            </Button>
          </Space>
        ) : (
          <Space>
            <Button
              icon={<EditOutlined />}
              type='primary'
              disabled={editingKey !== ''}
              onClick={() => edit(record)}
              size='small'
            >
              Edit
            </Button>
            <Popconfirm title='Sure to delete?' onConfirm={() => handleDelete(record.id)}>
              <Button danger icon={<DeleteOutlined />} size='small' disabled={editingKey !== ''}>
                Delete
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <Title level={2}>Fixed Expenses</Title>

      <FormCard title='Add New Fixed Expense'>
        <Form form={form} onFinish={handleSubmit} layout='vertical'>
          <Form.Item
            name='date'
            label='Date'
            rules={[{ required: true, message: 'Please select a date' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name='description'
            label='Description'
            rules={[{ required: true, message: 'Please enter a description' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name='amount'
            label='Amount'
            rules={[{ required: true, message: 'Please enter an amount' }]}
          >
            <CurrencyInput />
          </Form.Item>

          <Form.Item name='recurrence' label='Recurrence' initialValue='None'>
            <Select onChange={value => handleRecurrenceChange(value, form)} allowClear>
              <Option value='None'>None</Option>
              <Option value='Weekly'>Weekly</Option>
              <Option value='Bi-weekly'>Bi-weekly</Option>
              <Option value='Monthly'>Monthly</Option>
              <Option value='Quarterly'>Quarterly</Option>
              <Option value='Custom'>Custom</Option>
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.recurrence !== currentValues.recurrence
            }
          >
            {() =>
              form.getFieldValue('recurrence') === 'Custom' ? (
                <Form.Item
                  name='interval'
                  label='Interval (days)'
                  rules={[{ required: true, message: 'Please enter an interval' }]}
                >
                  <InputNumber min={1} style={{ width: '100%' }} />
                </Form.Item>
              ) : null
            }
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.recurrence !== currentValues.recurrence
            }
          >
            {() =>
              ['Monthly', 'Bi-weekly'].includes(form.getFieldValue('recurrence')) ? (
                <div style={{ display: 'flex', gap: '16px' }}>
                  <Form.Item name='week_of_month' label='Week of Month' style={{ flex: 1 }}>
                    <Select placeholder='Select week' allowClear>
                      <Option value={1}>1st</Option>
                      <Option value={2}>2nd</Option>
                      <Option value={3}>3rd</Option>
                      <Option value={4}>4th</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item name='day_of_week' label='Day of Week' style={{ flex: 1 }}>
                    <Select placeholder='Select day' allowClear>
                      <Option value={0}>Monday</Option>
                      <Option value={1}>Tuesday</Option>
                      <Option value={2}>Wednesday</Option>
                      <Option value={3}>Thursday</Option>
                      <Option value={4}>Friday</Option>
                      <Option value={5}>Saturday</Option>
                      <Option value={6}>Sunday</Option>
                    </Select>
                  </Form.Item>
                </div>
              ) : null
            }
          </Form.Item>

          <Form.Item name='tag_names' label='Tags'>
            <Select
              mode='tags'
              style={{ width: '100%' }}
              placeholder='Select or create tags'
              tokenSeparators={[',']}
              options={tagOptions}
            />
          </Form.Item>

          <Form.Item>
            <Button type='primary' htmlType='submit' loading={submitting}>
              Add Expense
            </Button>
          </Form.Item>
        </Form>
      </FormCard>

      <FormCard title='Existing Fixed Expenses'>
        <Form form={editForm} component={false}>
          <ClearFiltersButton onClear={clearFiltersAndSorting} />
          <LoadingTable<FixedExpense>
            loading={loading}
            columns={columns}
            dataSource={expenses}
            rowKey='id'
            pagination={{ defaultPageSize: 10, showSizeChanger: true }}
            onChange={handleTableChange}
            className='fixed-expenses-table'
            summary={() => (
              <Table.Summary fixed>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={2} align='right'>
                    <strong>Total:</strong>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={2}>
                    <strong>{formatCurrency(total)}</strong>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={3} />
                </Table.Summary.Row>
              </Table.Summary>
            )}
          />
        </Form>
      </FormCard>
    </div>
  );
};

export default FixedExpenses;
