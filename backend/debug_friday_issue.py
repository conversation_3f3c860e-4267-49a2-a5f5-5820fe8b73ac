#!/usr/bin/env python3
"""
Debug script to analyze the Friday issue with the forecast data.
"""

from datetime import datetime, timed<PERSON>ta


def get_fridays_of_month(year: int, month: int) -> list[datetime]:
    """Get all Fridays in a given month."""
    first_day = datetime(year, month, 1)
    first_friday = first_day + timedelta(days=(4 - first_day.weekday()) % 7)
    
    fridays = []
    current_friday = first_friday
    
    while current_friday.month == month:
        fridays.append(current_friday)
        current_friday += timedelta(days=7)
        
    return fridays


def analyze_forecast_data():
    """Analyze the forecast data to identify the issue."""
    
    # Week dates from the user's data
    week_dates = [
        "2025-02-23", "2025-03-02", "2025-03-09", "2025-03-16", "2025-03-23", "2025-03-30",
        "2025-04-06", "2025-04-13", "2025-04-20", "2025-04-27", "2025-05-04", "2025-05-11",
        "2025-05-18", "2025-05-25", "2025-06-01", "2025-06-08", "2025-06-15", "2025-06-22",
        "2025-06-29", "2025-07-06", "2025-07-13", "2025-07-20", "2025-07-27", "2025-08-03",
        "2025-08-10", "2025-08-17", "2025-08-24", "2025-08-31", "2025-09-07", "2025-09-14",
        "2025-09-21", "2025-09-28"
    ]
    
    # Payroll amounts from the user's data
    payroll_amounts = [
        0, 2600, 0, 2600, 0, 0, 0, 2600, 0, 0, 0, 2600, 0, 0, 2600, 0, 2600, 0, 0, 0, 2600, 0, 0, 0, 2600, 0, 0, 0, 0, 2600, 0, 0
    ]
    
    print("Analyzing forecast data for 1st & 3rd Fridays payroll:")
    print("=" * 60)
    
    # Convert week dates to datetime objects
    weeks = [datetime.strptime(date, "%Y-%m-%d") for date in week_dates]
    
    # Analyze each month in the range
    months_analyzed = set()
    
    for i, (week_start, amount) in enumerate(zip(weeks, payroll_amounts)):
        week_end = week_start + timedelta(days=6)
        
        # Get the month for this week
        month_key = (week_start.year, week_start.month)
        
        if month_key not in months_analyzed:
            months_analyzed.add(month_key)
            
            # Get all Fridays for this month
            fridays = get_fridays_of_month(week_start.year, week_start.month)
            
            print(f"\n{datetime(week_start.year, week_start.month, 1).strftime('%B %Y')}:")
            for j, friday in enumerate(fridays, 1):
                print(f"  {j}{'st' if j == 1 else 'nd' if j == 2 else 'rd' if j == 3 else 'th'} Friday: {friday.strftime('%Y-%m-%d')}")
            
            # Show which should be 1st & 3rd
            first_third_fridays = []
            if len(fridays) > 0:
                first_third_fridays.append(fridays[0])
            if len(fridays) > 2:
                first_third_fridays.append(fridays[2])
            
            print(f"  Expected 1st & 3rd Fridays: {[f.strftime('%Y-%m-%d') for f in first_third_fridays]}")
        
        # Check if this week should have payroll
        friday_in_week = None
        for day in range(7):
            current_day = week_start + timedelta(days=day)
            if current_day.weekday() == 4:  # Friday
                friday_in_week = current_day
                break
        
        if amount > 0:
            print(f"  Week {week_start.strftime('%Y-%m-%d')} - {week_end.strftime('%Y-%m-%d')}: "
                  f"Amount: ${amount}, Friday: {friday_in_week.strftime('%Y-%m-%d') if friday_in_week else 'None'}")
            
            # Check if this Friday should be a payroll Friday
            if friday_in_week:
                month_fridays = get_fridays_of_month(friday_in_week.year, friday_in_week.month)
                friday_index = None
                for idx, f in enumerate(month_fridays):
                    if f.date() == friday_in_week.date():
                        friday_index = idx + 1
                        break
                
                is_correct = friday_index in [1, 3]  # 1st or 3rd Friday
                print(f"    -> This is the {friday_index}{'st' if friday_index == 1 else 'nd' if friday_index == 2 else 'rd' if friday_index == 3 else 'th'} Friday of the month. "
                      f"Should have payroll: {is_correct}")


if __name__ == "__main__":
    analyze_forecast_data()
